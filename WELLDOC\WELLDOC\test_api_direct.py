#!/usr/bin/env python3
"""Direct API testing without running server"""

import json
from dotenv import load_dotenv
load_dotenv()

from app import create_app

def test_api():
    """Test API endpoints directly"""
    app = create_app()
    
    with app.test_client() as client:
        print("=== Testing API Endpoints ===")
        
        # Test health endpoint
        print("\n1. Testing health endpoint...")
        response = client.get('/health')
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.get_json()}")
        
        # Test registration
        print("\n2. Testing user registration...")
        user_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'password': 'password123',
            'age': 30,
            'gender': 'Male'
        }
        response = client.post('/register', json=user_data)
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.get_json()}")
        
        # Test login
        print("\n3. Testing user login...")
        login_data = {
            'email': '<EMAIL>',
            'password': 'password123'
        }
        response = client.post('/login', json=login_data)
        print(f"   Status: {response.status_code}")
        login_response = response.get_json()
        print(f"   Response: {login_response}")
        
        if response.status_code == 200 and 'access_token' in login_response:
            token = login_response['access_token']
            headers = {'Authorization': f'Bearer {token}'}
            
            # Test prediction endpoint
            print("\n4. Testing diabetes prediction...")
            prediction_data = {
                'age': 45,
                'glucose': 120,
                'bmi': 28
            }
            response = client.post('/predict/diabetes', 
                                 json=prediction_data, 
                                 headers=headers)
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.get_json()}")
            
            # Test hypertension prediction
            print("\n5. Testing hypertension prediction...")
            prediction_data = {
                'age': 65,
                'bmi': 32,
                'salt_intake': 12
            }
            response = client.post('/predict/hypertension', 
                                 json=prediction_data, 
                                 headers=headers)
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.get_json()}")
            
            # Test adding vitals
            print("\n6. Testing add vitals...")
            vitals_data = {
                'glucose': 110,
                'bp_sys': 120,
                'bp_dia': 80,
                'heart_rate': 72,
                'steps': 8000,
                'sleep_hours': 7.5
            }
            user_id = login_response['id']
            response = client.post(f'/patients/{user_id}/vitals', 
                                 json=vitals_data, 
                                 headers=headers)
            print(f"   Status: {response.status_code}")
            print(f"   Response: {response.get_json()}")
            
            # Test get vitals
            print("\n7. Testing get vitals...")
            response = client.get(f'/patients/{user_id}/vitals', 
                                headers=headers)
            print(f"   Status: {response.status_code}")
            vitals_response = response.get_json()
            print(f"   Response: Found {len(vitals_response)} vitals records")
        
        print("\n=== API Testing Complete ===")

if __name__ == '__main__':
    test_api()
