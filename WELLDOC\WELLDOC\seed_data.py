# seed_data.py
import os
from datetime import datetime, timedelta
from flask import Flask
from models import db, Patient, Vital, FoodLog, Symptom, MicroGoal, Gamification
from utils import hash_password
from dotenv import load_dotenv
import random

load_dotenv()

def create_app():
    app = Flask(__name__)
    app.config['SQLALCHEMY_DATABASE_URI'] = (
        f"mysql+pymysql://{os.getenv('DB_USER')}:{os.getenv('DB_PASS')}@{os.getenv('DB_HOST')}/{os.getenv('DB_NAME')}"
    )
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'devkey')
    db.init_app(app)
    return app

def seed_test_data():
    """Add test data to the database"""
    
    # Create test patients
    test_patients = [
        {
            'name': '<PERSON>',
            'email': '<EMAIL>',
            'password': 'password123',
            'age': 45,
            'gender': 'Male',
            'conditions': 'Type 2 Diabetes',
            'medications': 'Metformin 500mg',
            'allergies': 'None'
        },
        {
            'name': 'Jane Smith',
            'email': '<EMAIL>',
            'password': 'password123',
            'age': 38,
            'gender': 'Female',
            'conditions': 'Hypertension',
            'medications': 'Lisinopril 10mg',
            'allergies': 'Penicillin'
        },
        {
            'name': 'Bob Johnson',
            'email': '<EMAIL>',
            'password': 'password123',
            'age': 52,
            'gender': 'Male',
            'conditions': 'Diabetes, Hypertension',
            'medications': 'Metformin 1000mg, Amlodipine 5mg',
            'allergies': 'Sulfa drugs'
        }
    ]
    
    created_patients = []
    
    for patient_data in test_patients:
        # Check if patient already exists
        existing = Patient.query.filter_by(email=patient_data['email']).first()
        if existing:
            print(f"Patient {patient_data['email']} already exists, skipping...")
            created_patients.append(existing)
            continue
            
        patient = Patient(
            name=patient_data['name'],
            email=patient_data['email'],
            password_hash=hash_password(patient_data['password']),
            age=patient_data['age'],
            gender=patient_data['gender']
        )
        
        # Set encrypted fields
        patient.conditions = patient_data['conditions']
        patient.medications = patient_data['medications']
        patient.allergies = patient_data['allergies']
        
        db.session.add(patient)
        db.session.flush()  # Get the ID
        created_patients.append(patient)
        print(f"Created patient: {patient.name} ({patient.email})")
    
    db.session.commit()
    
    # Add vitals data for each patient
    for patient in created_patients:
        # Add 30 days of vitals data
        for i in range(30):
            date = datetime.utcnow() - timedelta(days=i)
            
            vital = Vital(
                patient_id=patient.id,
                timestamp=date,
                glucose=random.randint(80, 200),  # mg/dL
                bp_sys=random.randint(110, 160),  # systolic
                bp_dia=random.randint(70, 100),   # diastolic
                heart_rate=random.randint(60, 100),
                steps=random.randint(2000, 12000),
                sleep_hours=random.uniform(5.0, 9.0)
            )
            db.session.add(vital)
        
        # Add some food logs
        foods = ['Oatmeal', 'Chicken Salad', 'Apple', 'Salmon', 'Broccoli', 'Rice', 'Yogurt']
        for i in range(10):
            date = datetime.utcnow() - timedelta(days=i)
            food = FoodLog(
                patient_id=patient.id,
                timestamp=date,
                food_item=random.choice(foods),
                calories=random.randint(100, 500),
                sodium_mg=random.randint(50, 1000)
            )
            db.session.add(food)
        
        # Add some symptoms
        symptoms = ['Fatigue', 'Headache', 'Dizziness', 'Nausea', 'Chest pain']
        severities = ['Mild', 'Moderate', 'Severe']
        for i in range(5):
            date = datetime.utcnow() - timedelta(days=i*2)
            symptom = Symptom(
                patient_id=patient.id,
                timestamp=date,
                symptom=random.choice(symptoms),
                severity=random.choice(severities)
            )
            db.session.add(symptom)
        
        # Add micro goals
        goal_types = ['steps', 'glucose', 'weight', 'exercise']
        for goal_type in goal_types:
            goal = MicroGoal(
                patient_id=patient.id,
                goal_type=goal_type,
                target_value=random.randint(5000, 10000) if goal_type == 'steps' else random.randint(80, 120),
                achieved_value=random.randint(4000, 9000) if goal_type == 'steps' else random.randint(75, 115),
                date=datetime.utcnow().date()
            )
            db.session.add(goal)
        
        # Add gamification data
        gamify = Gamification(
            patient_id=patient.id,
            points=random.randint(100, 1000),
            badges=random.randint(1, 10),
            streaks=random.randint(1, 30)
        )
        db.session.add(gamify)
    
    db.session.commit()
    print(f"Successfully seeded test data for {len(created_patients)} patients!")

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        seed_test_data()
