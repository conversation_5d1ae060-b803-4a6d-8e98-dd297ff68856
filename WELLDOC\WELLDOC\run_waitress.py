# run_waitress.py — loads .env first so secure_utils sees FERNET_KEY
from dotenv import load_dotenv
import os
from pathlib import Path

# ensure .env in project root is loaded
proj_root = Path(__file__).resolve().parent
dotenv_path = proj_root / ".env"
load_dotenv(dotenv_path=dotenv_path)

# optional sanity check (prints when running)
print("FERNET_KEY present:", bool(os.getenv("FERNET_KEY")))

from waitress import serve
from app import create_app   # safe now: envvars loaded
app = create_app()

print("Starting Waitress server on http://127.0.0.1:5000")
serve(app, host="127.0.0.1", port=5000)
