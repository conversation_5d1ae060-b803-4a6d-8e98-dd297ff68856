# secure_utils.py
import os
from cryptography.fernet import <PERSON><PERSON><PERSON>, InvalidToken
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

FERNET_KEY = os.getenv("FERNET_KEY")
if not FERNET_KEY:
    raise RuntimeError("FERNET_KEY not found in environment (set a base64 urlsafe key)")

if isinstance(FERNET_KEY, str):
    FERNET_KEY_BYTES = FERNET_KEY.encode()
else:
    FERNET_KEY_BYTES = FERNET_KEY

fernet = Fernet(FERNET_KEY_BYTES)

def encrypt_bytes(b: bytes) -> bytes:
    return fernet.encrypt(b)

def decrypt_bytes(token: bytes) -> bytes:
    return fernet.decrypt(token)

def encrypt_text(s: str) -> str:
    if s is None: return None
    return fernet.encrypt(s.encode()).decode()

def decrypt_text(token: str) -> str:
    if token is None: return None
    try:
        return fernet.decrypt(token.encode()).decode()
    except InvalidToken:
        return None
