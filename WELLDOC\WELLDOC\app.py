# app.py
import os
import json
from datetime import datetime, timedelta
from flask import Flask, request, jsonify, send_file, abort
from dotenv import load_dotenv
from models import db, Patient, Vital, FoodLog, Symptom, MicroGoal, Gamification, Consent, AuditLog
from utils import hash_password, verify_password
from utils_audit import log_audit
from model_server import get_model_server, DIABETES_KEY, HYPERTENSION_KEY
from secure_utils import encrypt_text, decrypt_text
from sqlalchemy.exc import IntegrityError
from flask_jwt_extended import (
    JWTManager, create_access_token, jwt_required, get_jwt_identity, get_jwt
)
import io, zipfile

load_dotenv()

def create_app():
    app = Flask(__name__)

    # --- Config ---
    app.config['SQLALCHEMY_DATABASE_URI'] = (
        f"mysql+pymysql://{os.getenv('DB_USER')}:{os.getenv('DB_PASS')}@{os.getenv('DB_HOST')}/{os.getenv('DB_NAME')}"
    )
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'devkey')

    # JWT config (separate secret recommended)
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', os.getenv('SECRET_KEY'))
    jwt_access_minutes = int(os.getenv('JWT_ACCESS_EXPIRES_MIN', '60'))
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(minutes=jwt_access_minutes)

    jwt = JWTManager(app)
    db.init_app(app)

    def get_current_user():
        """Helper function to get current user info from JWT"""
        user_id = int(get_jwt_identity())
        claims = get_jwt()
        role = claims.get('role', 'patient')
        return {'id': user_id, 'role': role}

    # health
    @app.route('/health', methods=['GET'])
    def health():
        return jsonify({"status":"ok", "time": datetime.utcnow().isoformat()})

    # ----- Auth endpoints -----
    @app.route('/register', methods=['POST'])
    def register():
        data = request.get_json(force=True, silent=True) or {}
        name = data.get('name'); email = data.get('email'); password = data.get('password')
        if not (name and email and password):
            return jsonify({"error":"name,email,password required"}), 400
        pw_hash = hash_password(password)
        # store encrypted sensitive fields if present
        conditions = data.get('conditions')
        medications = data.get('medications')
        allergies = data.get('allergies')
        p = Patient(
            name=name,
            email=email,
            password_hash=pw_hash,
            age=data.get('age'),
            gender=data.get('gender')
        )
        # Use property setters (see models.py) or set encrypted fields directly
        if conditions: p.conditions = conditions
        if medications: p.medications = medications
        if allergies: p.allergies = allergies
        try:
            db.session.add(p); db.session.commit()
            log_audit(actor_id=p.id, actor_role='patient', action='REGISTER', target_table='patients', target_id=p.id, details={"email": p.email})
            return jsonify({"id": p.id, "email": p.email}), 201
        except IntegrityError:
            db.session.rollback()
            return jsonify({"error":"email exists"}), 409

    @app.route('/login', methods=['POST'])
    def login():
        data = request.get_json(force=True, silent=True) or {}
        email = data.get('email'); password = data.get('password')
        if not (email and password):
            return jsonify({"error":"email,password required"}), 400
        patient = Patient.query.filter_by(email=email).first()
        if not patient or not verify_password(patient.password_hash, password):
            # audit failed login attempt (no PII in details)
            log_audit(actor_id=None, actor_role='anonymous', action='LOGIN_FAILED', target_table='patients', target_id=None, details={"email": email})
            return jsonify({"error":"invalid credentials"}), 401
        identity = str(patient.id)  # JWT subject must be string
        access_token = create_access_token(identity=identity, additional_claims={"role": "patient"})
        log_audit(actor_id=patient.id, actor_role='patient', action='LOGIN', target_table='patients', target_id=patient.id, details={})
        return jsonify({"id": patient.id, "access_token": access_token})

    # ----- Protected example: get patient (patient or clinician) -----
    @app.route('/patients/<int:pid>', methods=['GET'])
    @jwt_required()
    def get_patient(pid):
        identity = get_current_user()
        # allow patient to access their data or clinician/admin role (not implemented here)
        if identity['role'] == 'patient' and identity['id'] != pid:
            return jsonify({"error":"forbidden"}), 403
        p = Patient.query.get_or_404(pid)
        # decrypt fields before returning
        out = {
            "id": p.id,
            "name": p.name,
            "email": p.email,
            "age": p.age,
            "gender": p.gender,
            "conditions": p.conditions,
            "medications": p.medications,
            "allergies": p.allergies,
            "created_at": p.created_at.isoformat() if p.created_at else None
        }
        return jsonify(out)

    # ----- Vitals endpoints (protected & audited) -----
    @app.route('/patients/<int:pid>/vitals', methods=['GET'])
    @jwt_required()
    def get_vitals(pid):
        identity = get_current_user()
        # patient can only get their own data (unless clinician role)
        if identity['role'] == 'patient' and identity['id'] != pid:
            return jsonify({"error":"forbidden"}), 403
        limit = int(request.args.get('limit', 100))
        vitals = Vital.query.filter_by(patient_id=pid).order_by(Vital.timestamp.desc()).limit(limit).all()
        out = []
        for v in vitals:
            out.append({
                "id": v.id, "timestamp": v.timestamp.isoformat(), "glucose": v.glucose,
                "bp_sys": v.bp_sys, "bp_dia": v.bp_dia, "heart_rate": v.heart_rate,
                "steps": v.steps, "sleep_hours": v.sleep_hours
            })
        return jsonify(out)

    @app.route('/patients/<int:pid>/vitals', methods=['POST'])
    @jwt_required()
    def add_vital(pid):
        identity = get_current_user()
        if identity['role'] == 'patient' and identity['id'] != pid:
            return jsonify({"error":"forbidden"}), 403
        data = request.get_json(force=True, silent=True) or {}
        v = Vital(
            patient_id=pid,
            timestamp = datetime.fromisoformat(data.get("timestamp")) if data.get("timestamp") else datetime.utcnow(),
            glucose = data.get("glucose"),
            bp_sys = data.get("bp_sys"),
            bp_dia = data.get("bp_dia"),
            heart_rate = data.get("heart_rate"),
            steps = data.get("steps"),
            sleep_hours = data.get("sleep_hours")
        )
        db.session.add(v); db.session.commit()
        # audit (minimal details)
        log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action="CREATE_VITAL", target_table="vitals", target_id=v.id,
                  details={"glucose_present": bool(data.get("glucose")), "bp_present": bool(data.get("bp_sys") or data.get("bp_dia"))})
        return jsonify({"id": v.id}), 201

    # ----- Prediction endpoints (use model_server) -----
    def _extract_features_from_body(body):
        if not body:
            return {}, None
        features = body.get('features') if 'features' in body else {k:v for k,v in body.items() if k not in ('model','expected_cols','patient_id')}
        expected_cols = body.get('expected_cols')
        return features, expected_cols

    @app.route('/predict', methods=['POST'])
    @jwt_required()
    def predict():
        identity = get_current_user()
        body = request.get_json(force=True, silent=True) or {}
        specified_model = (request.args.get('model') or body.get('model') or DIABETES_KEY).lower()
        features, expected_cols = _extract_features_from_body(body)
        if not features:
            return jsonify({"error":"no features provided"}), 400
        try:
            ms = get_model_server()
            res = ms.predict(specified_model, features, expected_cols=expected_cols)
            # audit prediction request (no raw PHI in details)
            log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action="PREDICT",
                      target_table="models", target_id=specified_model, details={"features_keys": list(features.keys())})
            return jsonify({"model": specified_model, "result": res})
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    @app.route('/predict/diabetes', methods=['POST'])
    @jwt_required()
    def predict_diabetes():
        identity = get_current_user()
        body = request.get_json(force=True, silent=True) or {}
        features, expected_cols = _extract_features_from_body(body)
        if not features:
            return jsonify({"error":"no features provided"}), 400
        try:
            ms = get_model_server()
            res = ms.predict(DIABETES_KEY, features, expected_cols=expected_cols)
            log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action="PREDICT_DIABETES",
                      target_table="models", target_id=DIABETES_KEY, details={"features_keys": list(features.keys())})
            return jsonify({"model": DIABETES_KEY, "result": res})
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    @app.route('/predict/hypertension', methods=['POST'])
    @jwt_required()
    def predict_hypertension():
        identity = get_current_user()
        body = request.get_json(force=True, silent=True) or {}
        features, expected_cols = _extract_features_from_body(body)
        if not features:
            return jsonify({"error":"no features provided"}), 400
        try:
            ms = get_model_server()
            res = ms.predict(HYPERTENSION_KEY, features, expected_cols=expected_cols)
            log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action="PREDICT_HYPERTENSION",
                      target_table="models", target_id=HYPERTENSION_KEY, details={"features_keys": list(features.keys())})
            return jsonify({"model": HYPERTENSION_KEY, "result": res})
        except Exception as e:
            return jsonify({"error": str(e)}), 500

    # ----- Food, symptoms, goals, gamification endpoints (protected) -----
    @app.route('/patients/<int:pid>/food', methods=['POST'])
    @jwt_required()
    def add_food(pid):
        identity = get_jwt_identity()
        if identity['role'] == 'patient' and identity['id'] != pid:
            return jsonify({"error":"forbidden"}), 403
        data = request.get_json(force=True, silent=True) or {}
        f = FoodLog(patient_id=pid, timestamp=datetime.fromisoformat(data.get('timestamp')) if data.get('timestamp') else datetime.utcnow(),
                    food_item=data.get('food_item'), calories=data.get('calories'), sodium_mg=data.get('sodium_mg'))
        db.session.add(f); db.session.commit()
        log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action="CREATE_FOOD", target_table="food_logs", target_id=f.id,
                  details={"food_item_present": bool(data.get('food_item'))})
        return jsonify({"id": f.id}), 201

    @app.route('/patients/<int:pid>/symptoms', methods=['POST'])
    @jwt_required()
    def add_symptom(pid):
        identity = get_jwt_identity()
        if identity['role'] == 'patient' and identity['id'] != pid:
            return jsonify({"error":"forbidden"}), 403
        data = request.get_json(force=True, silent=True) or {}
        s = Symptom(patient_id=pid, timestamp=datetime.fromisoformat(data.get('timestamp')) if data.get('timestamp') else datetime.utcnow(),
                    symptom=data.get('symptom'), severity=data.get('severity'))
        db.session.add(s); db.session.commit()
        log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action="CREATE_SYMPTOM", target_table="symptoms", target_id=s.id,
                  details={"symptom_present": bool(data.get('symptom'))})
        return jsonify({"id": s.id}), 201

    @app.route('/patients/<int:pid>/goals', methods=['POST'])
    @jwt_required()
    def add_goal(pid):
        identity = get_jwt_identity()
        if identity['role'] == 'patient' and identity['id'] != pid:
            return jsonify({"error":"forbidden"}), 403
        data = request.get_json(force=True, silent=True) or {}
        g = MicroGoal(patient_id=pid, goal_type=data.get('goal_type'), target_value=data.get('target_value'),
                      achieved_value=data.get('achieved_value'), date=data.get('date'))
        db.session.add(g); db.session.commit()
        log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action="CREATE_GOAL", target_table="micro_goals", target_id=g.id, details={"goal_type": g.goal_type})
        return jsonify({"id": g.id}), 201

    @app.route('/patients/<int:pid>/gamify', methods=['POST'])
    @jwt_required()
    def update_gamify(pid):
        identity = get_jwt_identity()
        if identity['role'] == 'patient' and identity['id'] != pid:
            return jsonify({"error":"forbidden"}), 403
        data = request.get_json(force=True, silent=True) or {}
        g = Gamification.query.filter_by(patient_id=pid).first()
        if not g:
            g = Gamification(patient_id=pid, points=data.get('points',0), badges=data.get('badges',0), streaks=data.get('streaks',0))
            db.session.add(g)
        else:
            g.points = data.get('points', g.points)
            g.badges = data.get('badges', g.badges)
            g.streaks = data.get('streaks', g.streaks)
        db.session.commit()
        log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action="UPDATE_GAMIFY", target_table="gamification", target_id=g.id, details={"points": g.points})
        return jsonify({"id": g.id}), 200

    # ----- Consent endpoints -----
    @app.route('/patients/<int:pid>/consent', methods=['POST'])
    @jwt_required()
    def give_consent(pid):
        identity = get_jwt_identity()
        if identity['role'] == 'patient' and identity['id'] != pid:
            return jsonify({"error":"forbidden"}), 403
        body = request.get_json(force=True, silent=True) or {}
        version = body.get('version','v1')
        text = body.get('text','Consent text...')
        c = Consent(patient_id=pid, consent_version=version, consent_text=text, granted=True)
        db.session.add(c); db.session.commit()
        log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action='GIVE_CONSENT', target_table='consents', target_id=c.id, details={"version":version})
        return jsonify({"id":c.id}), 201

    @app.route('/patients/<int:pid>/consent/revoke', methods=['POST'])
    @jwt_required()
    def revoke_consent(pid):
        identity = get_jwt_identity()
        if identity['role'] == 'patient' and identity['id'] != pid:
            return jsonify({"error":"forbidden"}), 403
        c = Consent.query.filter_by(patient_id=pid, granted=True).order_by(Consent.granted_at.desc()).first()
        if not c: return jsonify({"error":"no granted consent found"}), 404
        c.granted = False
        c.revoked_at = datetime.utcnow()
        db.session.commit()
        log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action='REVOKE_CONSENT', target_table='consents', target_id=c.id, details={})
        return jsonify({"revoked": True})

    # ----- Export data (patient request) -----
    @app.route('/patients/<int:pid>/export', methods=['GET'])
    @jwt_required()
    def export_patient(pid):
        identity = get_jwt_identity()
        if identity['role'] == 'patient' and identity['id'] != pid:
            return jsonify({"error":"forbidden"}), 403

        patient = Patient.query.get_or_404(pid)
        # gather related records
        vitals = [ {"timestamp":v.timestamp.isoformat(), "glucose":v.glucose, "bp_sys":v.bp_sys, "bp_dia":v.bp_dia, "heart_rate":v.heart_rate, "steps":v.steps, "sleep_hours":v.sleep_hours} for v in Vital.query.filter_by(patient_id=pid).all() ]
        food = [ {"timestamp":f.timestamp.isoformat(), "food_item":f.food_item, "calories":f.calories, "sodium_mg":f.sodium_mg} for f in FoodLog.query.filter_by(patient_id=pid).all() ]
        symptoms = [ {"timestamp":s.timestamp.isoformat(), "symptom":s.symptom, "severity":s.severity} for s in Symptom.query.filter_by(patient_id=pid).all() ]
        goals = [ {"goal_type":g.goal_type, "target_value":g.target_value, "achieved_value":g.achieved_value, "date": str(g.date)} for g in MicroGoal.query.filter_by(patient_id=pid).all() ]
        gamify = Gamification.query.filter_by(patient_id=pid).first()
        bundle = {
            "patient": {"id":patient.id, "name":patient.name, "email":patient.email, "age":patient.age, "gender":patient.gender},
            "vitals": vitals, "food": food, "symptoms": symptoms, "goals": goals,
            "gamification": {"points": gamify.points, "badges": getattr(gamify, "badges", None), "streaks": getattr(gamify, "streaks", None)} if gamify else {}
        }
        bio = io.BytesIO()
        with zipfile.ZipFile(bio, mode='w') as zf:
            zf.writestr('data.json', json.dumps(bundle, default=str))
        bio.seek(0)
        log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action='EXPORT_DATA', target_table='patients', target_id=pid, details={})
        return send_file(bio, as_attachment=True, download_name=f'patient_{pid}_export.zip', mimetype='application/zip')

    # ----- Delete request (soft delete) -----
    @app.route('/patients/<int:pid>/delete', methods=['POST'])
    @jwt_required()
    def request_delete(pid):
        identity = get_jwt_identity()
        if identity['role'] == 'patient' and identity['id'] != pid:
            return jsonify({"error":"forbidden"}), 403
        p = Patient.query.get_or_404(pid)
        # ensure Patient model has `deleted` and `deleted_at` columns (see models.py changes)
        p.deleted = True
        p.deleted_at = datetime.utcnow()
        db.session.commit()
        log_audit(actor_id=identity['id'], actor_role=identity.get('role'), action='REQUEST_DELETE', target_table='patients', target_id=pid, details={})
        return jsonify({"requested": True})

    return app

if __name__ == '__main__':
    app = create_app()
    with app.app_context():
        db.create_all()
    app.run(debug=True, port=5000)
