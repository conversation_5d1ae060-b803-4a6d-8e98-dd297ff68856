#!/usr/bin/env python3
"""Test script to debug server startup issues"""

import sys
import traceback
from dotenv import load_dotenv
import os

print("=== Testing Server Startup ===")

try:
    print("1. Loading environment variables...")
    load_dotenv()
    print(f"   FERNET_KEY present: {bool(os.getenv('FERNET_KEY'))}")
    print(f"   DB_HOST: {os.getenv('DB_HOST')}")
    print(f"   DB_NAME: {os.getenv('DB_NAME')}")
    
    print("2. Testing database connection...")
    import pymysql
    connection = pymysql.connect(
        host=os.getenv('DB_HOST'),
        user=os.getenv('DB_USER'),
        password=os.getenv('DB_PASS'),
        database=os.getenv('DB_NAME')
    )
    print("   Database connection successful!")
    connection.close()
    
    print("3. Testing model server...")
    from model_server import get_model_server
    ms = get_model_server()
    print(f"   Models loaded: {list(ms.models.keys())}")
    
    print("4. Creating Flask app...")
    from app import create_app
    app = create_app()
    print("   Flask app created successfully!")
    
    print("5. Testing app context...")
    with app.app_context():
        from models import db
        print("   Database models imported successfully!")
    
    print("6. Starting server...")
    print("   Server will start on http://127.0.0.1:5000")
    print("   Press Ctrl+C to stop")
    app.run(debug=True, port=5000, host='127.0.0.1', use_reloader=False)
    
except Exception as e:
    print(f"ERROR: {e}")
    print("Full traceback:")
    traceback.print_exc()
    sys.exit(1)
