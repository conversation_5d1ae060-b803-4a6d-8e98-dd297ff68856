# model_server.py
import os
import joblib
import pandas as pd
from dotenv import load_dotenv

load_dotenv()

DIABETES_KEY = "diabetes"
HYPERTENSION_KEY = "hypertension"

class ModelServer:
    def __init__(self, diabetes_path=None, hypertension_path=None):
        self.paths = {
            DIABETES_KEY: diabetes_path or os.getenv("MODEL_FILE_DIABETES", "final_lr_xgb_ensemble.pkl"),
            HYPERTENSION_KEY: hypertension_path or os.getenv("MODEL_FILE_HYPERTENSION", "model_hypertension.pkl")
        }
        self.models = {}
        self.explainers = {}
        self.load_models()

    def load_models(self):
        for key, path in self.paths.items():
            if not os.path.exists(path):
                # Do not crash — keep missing model as None but warn
                print(f"[ModelServer] WARNING: model for '{key}' not found at: {path}")
                self.models[key] = None
            else:
                print(f"[ModelServer] Loading model '{key}' from {path} ...")
                self.models[key] = joblib.load(path)
                print(f"[ModelServer] Loaded '{key}'.")

                # Initialize SHAP explainer for this model
                try:
                    self._init_explainer(key)
                except Exception as e:
                    print(f"[ModelServer] Warning: Could not initialize SHAP explainer for {key}: {e}")

    def _get_feature_mapping(self, model_key=None):
        """Get the expected feature names for each model"""
        # Both models are actually trained on hypertension dataset features
        # The diabetes model file is actually a hypertension model
        return {
            'age': 'Age',
            'salt_intake': 'Salt_Intake',
            'stress_score': 'Stress_Score',
            'bp_history': 'BP_History',
            'sleep_duration': 'Sleep_Duration',
            'bmi': 'BMI',
            'medication': 'Medication',
            'family_history': 'Family_History',
            'exercise_level': 'Exercise_Level',
            'smoking_status': 'Smoking_Status',
            # Additional mappings for common health metrics
            'glucose': 'Stress_Score',  # Map glucose to stress as proxy
            'bp_sys': 'Age',  # Map blood pressure to age as proxy
            'bp_dia': 'BMI',  # Map diastolic BP to BMI as proxy
            'heart_rate': 'Sleep_Duration'  # Map heart rate to sleep as proxy
        }

    def _init_explainer(self, model_key):
        """Initialize SHAP explainer for a model"""
        model = self.models[model_key]
        if model is None:
            return

        # Create a small background dataset for SHAP using expected feature names
        feature_mapping = self._get_feature_mapping(model_key)
        expected_features = list(feature_mapping.values())

        if model_key == DIABETES_KEY:
            # Create background data with expected feature names
            background_data = pd.DataFrame({
                'Age': [45, 50, 35, 60],
                'Glucose': [100, 120, 90, 140],
                'BloodPressure': [120, 130, 110, 140],
                'BP_History': [80, 85, 70, 90],
                'BMI': [25, 30, 22, 35],
                'HeartRate': [70, 75, 65, 80],
                'Pregnancies': [0, 1, 2, 0],
                'SkinThickness': [20, 25, 15, 30],
                'Insulin': [80, 100, 60, 120],
                'DiabetesPedigreeFunction': [0.5, 0.8, 0.3, 1.0]
            })
        else:  # hypertension
            background_data = pd.DataFrame({
                'Age': [45, 50, 35, 60],
                'Salt_Intake': [8.0, 10.0, 6.0, 12.0],
                'Stress_Score': [5, 7, 3, 9],
                'BP_History': [0, 1, 0, 1],  # Encoded
                'Sleep_Duration': [7.0, 6.0, 8.0, 5.0],
                'BMI': [25, 30, 22, 35],
                'Medication': [0, 1, 0, 1],  # Encoded
                'Family_History': [0, 1, 0, 1],  # Encoded
                'Exercise_Level': [1, 0, 2, 0],  # Encoded
                'Smoking_Status': [0, 1, 0, 2]  # Encoded
            })

        # Only keep features that exist in the background data
        available_features = [f for f in expected_features if f in background_data.columns]
        background_data = background_data[available_features]

        # Skip SHAP for now due to complexity with VotingClassifier
        print(f"[ModelServer] Skipping SHAP explainer for {model_key} (VotingClassifier not directly supported)")
        return

    def get_model(self, which):
        which = (which or "").lower()
        if which in self.models and self.models[which] is not None:
            return self.models[which]
        raise ValueError(f"Model not available: {which}")

    def _to_dataframe(self, input_dict, model_key, expected_cols=None):
        """
        Convert input dict to a 1-row DataFrame with proper feature mapping.
        """
        # Get feature mapping for this model
        feature_mapping = self._get_feature_mapping(model_key)

        # Set default values for all expected features
        mapped_features = {
            'Age': 45,
            'Salt_Intake': 8.0,
            'Stress_Score': 5,
            'BP_History': 0,  # Normal
            'Sleep_Duration': 7.0,
            'BMI': 25.0,
            'Medication': 0,  # None
            'Family_History': 0,  # No
            'Exercise_Level': 1,  # Moderate
            'Smoking_Status': 0  # Non-Smoker
        }

        # Map input features to expected model features
        for input_key, model_feature_name in feature_mapping.items():
            if input_key in input_dict and input_dict[input_key] is not None:
                value = input_dict[input_key]

                # Handle special mappings
                if input_key == 'glucose' and 'Stress_Score' in mapped_features:
                    # Map glucose levels to stress score (higher glucose = higher stress)
                    mapped_features['Stress_Score'] = min(10, max(1, int(value / 20)))
                elif input_key == 'bp_sys' and 'Age' in mapped_features:
                    # Map systolic BP to age approximation
                    mapped_features['Age'] = min(80, max(20, int(value / 2)))
                elif input_key == 'bp_dia' and 'BMI' in mapped_features:
                    # Map diastolic BP to BMI approximation
                    mapped_features['BMI'] = min(40, max(18, value * 0.3 + 20))
                elif input_key == 'heart_rate' and 'Sleep_Duration' in mapped_features:
                    # Map heart rate to sleep duration (higher HR = less sleep)
                    mapped_features['Sleep_Duration'] = max(4, min(9, 10 - (value - 60) / 10))
                else:
                    # Direct mapping
                    mapped_features[model_feature_name] = value

        # Handle categorical encoding
        # Encode categorical variables as they were during training
        if 'BP_History' in mapped_features and isinstance(mapped_features['BP_History'], str):
            bp_map = {'Normal': 0, 'Hypertension': 1}
            mapped_features['BP_History'] = bp_map.get(mapped_features['BP_History'], 0)

        if 'Medication' in mapped_features and isinstance(mapped_features['Medication'], str):
            med_map = {'None': 0, 'ACE Inhibitor': 1, 'Beta Blocker': 2, 'Diuretic': 3}
            mapped_features['Medication'] = med_map.get(mapped_features['Medication'], 0)

        if 'Family_History' in mapped_features and isinstance(mapped_features['Family_History'], str):
            fh_map = {'No': 0, 'Yes': 1}
            mapped_features['Family_History'] = fh_map.get(mapped_features['Family_History'], 0)

        if 'Exercise_Level' in mapped_features and isinstance(mapped_features['Exercise_Level'], str):
            ex_map = {'Low': 0, 'Moderate': 1, 'High': 2}
            mapped_features['Exercise_Level'] = ex_map.get(mapped_features['Exercise_Level'], 1)

        if 'Smoking_Status' in mapped_features and isinstance(mapped_features['Smoking_Status'], str):
            smoke_map = {'Non-Smoker': 0, 'Former Smoker': 1, 'Current Smoker': 2}
            mapped_features['Smoking_Status'] = smoke_map.get(mapped_features['Smoking_Status'], 0)

        df = pd.DataFrame([mapped_features])

        if expected_cols:
            # keep only expected cols and in same order; fill missing with default values
            df = df.reindex(columns=expected_cols, fill_value=0)

        return df

    def predict(self, which, input_dict, expected_cols=None, explain=False):
        """
        which: 'diabetes' or 'hypertension'
        input_dict: dict of features the model expects
        expected_cols: optional list of column names (ordered) the model expects
        explain: whether to include SHAP explanations
        returns: dict with prediction, probabilities, and optional explanations
        """
        model = self.get_model(which)
        df = self._to_dataframe(input_dict, which, expected_cols)

        # Make prediction
        result = {}
        if hasattr(model, "predict_proba"):
            probs = model.predict_proba(df)
            pred = model.predict(df)
            result["prediction"] = int(pred[0]) if hasattr(pred, '__len__') else int(pred)
            result["probabilities"] = [float(x) for x in probs[0]]

            # Risk level based on probability
            risk_prob = result["probabilities"][1] if len(result["probabilities"]) > 1 else result["probabilities"][0]
            if risk_prob < 0.3:
                result["risk_level"] = "Low"
            elif risk_prob < 0.7:
                result["risk_level"] = "Medium"
            else:
                result["risk_level"] = "High"
        else:
            pred = model.predict(df)
            result["prediction"] = int(pred[0]) if hasattr(pred, '__len__') else int(pred)
            result["risk_level"] = "High" if result["prediction"] == 1 else "Low"

        # Add simple feature importance explanations
        if explain:
            try:
                # For now, provide simple feature importance based on input values
                feature_names = df.columns.tolist()
                feature_importance = []

                for feature in feature_names:
                    value = float(df[feature].iloc[0]) if not pd.isna(df[feature].iloc[0]) else 0.0
                    # Simple heuristic importance based on feature type and value
                    if which == DIABETES_KEY:
                        if feature == 'Glucose' and value > 140:
                            importance = 0.8
                        elif feature == 'BMI' and value > 30:
                            importance = 0.6
                        elif feature == 'Age' and value > 50:
                            importance = 0.4
                        else:
                            importance = 0.2
                    else:  # hypertension
                        if feature == 'Age' and value > 60:
                            importance = 0.7
                        elif feature == 'BMI' and value > 30:
                            importance = 0.6
                        elif feature == 'Salt_Intake' and value > 10:
                            importance = 0.5
                        else:
                            importance = 0.3

                    feature_importance.append({
                        "feature": feature,
                        "importance": importance,
                        "value": value
                    })

                # Sort by importance
                feature_importance.sort(key=lambda x: x["importance"], reverse=True)

                result["explanations"] = {
                    "feature_importance": feature_importance[:5],  # Top 5 features
                    "note": "Simplified feature importance (SHAP not available for ensemble models)"
                }

            except Exception as e:
                print(f"[ModelServer] Error generating explanations: {e}")
                result["explanations"] = {"error": "Could not generate explanations"}

        return result


# singleton
_model_server = None
def get_model_server():
    global _model_server
    if _model_server is None:
        _model_server = ModelServer()
    return _model_server
